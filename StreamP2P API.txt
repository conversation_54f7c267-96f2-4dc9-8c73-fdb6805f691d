{"openapi": "3.1.0", "info": {"title": "StreamP2P API", "description": "APIs are available for all active users", "version": "1.0.0"}, "servers": [{"url": "https://streamp2p.com", "description": "Production"}], "paths": {"/api/v1/billing/balance": {"get": {"tags": ["Billing"], "summary": "Get user balance", "operationId": "c932f3d82a52a78de244d6f525553c75", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}, "example": {"id": "id", "service": "service_name", "available": 100, "pending": 50, "paidAmount": 50}}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/balance/history": {"get": {"tags": ["Billing"], "summary": "Get user balance history", "operationId": "6361dbee0a14b38fc0238418960333c2", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "User balance history", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"description": "description", "amount": 100, "service": "service", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/invoice": {"get": {"tags": ["Billing"], "summary": "Get user invoices", "operationId": "f4c2938bfedc0883318e44c310ffc25a", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "User invoices", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "id", "status": "status", "amount": 100, "paidAmount": 100, "service": "service", "isWithdrawal": true, "profile": "profile_id", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Billing"], "summary": "Create invoice", "operationId": "77cd71ed99d2788a78389a1b581ccd9e", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"balanceId": {"type": "string"}, "amount": {"type": "number"}, "profileId": {"type": "string"}, "coupon": {"type": "string"}}, "type": "object"}, "example": {"balanceId": "id", "amount": 100, "profileId": "id", "coupon": "coupon"}}}}, "responses": {"201": {"description": "Invoice created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/invoice/{id}": {"get": {"tags": ["Billing"], "summary": "Get user invoice", "operationId": "3cdbf88154ca74dfc16cd3a2c05024b3", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "status": {"type": "string"}, "amount": {"type": "number"}, "paidAmount": {"type": "number"}, "service": {"type": "string"}, "isWithdrawal": {"type": "boolean"}, "profile": {"type": "string"}, "createdAt": {"type": "string"}, "user": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "organization": {"type": "string"}}, "type": "object"}, "transaction": {"type": "array", "items": {"properties": {"id": {"type": "string"}, "transactionId": {"type": "string"}, "amount": {"type": "number"}, "currencyAmount": {"type": "number"}, "currency": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}}}, "type": "object"}, "example": {"id": "id", "status": "status", "amount": 100, "paidAmount": 100, "service": "service", "isWithdrawal": true, "profile": "profile_id", "createdAt": "2024-01-01 00:00:00", "user": {"id": "id", "name": "name", "email": "email", "organization": "organization"}, "transaction": [{"id": "id", "transactionId": "transactionId", "amount": 100, "currencyAmount": 10, "currency": "currency", "createdAt": "2024-01-01 00:00:00"}]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Billing"], "summary": "Delete cancelled invoice", "operationId": "82dfd17102fb6e1e71d0f08f645b40ac", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "Invoice deleted"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/invoice/{id}/cancel": {"put": {"tags": ["Billing"], "summary": "Cancel the pending invoice", "operationId": "305f9760b2b3116b7e9e0e0cfbd5b007", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "Invoice cancelled"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/paygate": {"get": {"tags": ["Billing"], "summary": "Get paygates", "operationId": "bb4df613db56f1c4c8f21efac4f40173", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "Paygates", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "id", "name": "name", "currency": "USD", "isCrypto": true, "isActive": true, "priority": 0, "minUSD": 0, "maxUSD": 0, "profileShape": {"type": "object"}}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/profile": {"get": {"tags": ["Billing"], "summary": "Get user payment profile", "operationId": "f48b605729735f9725a0ae74bd5b10e5", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "id", "name": "name", "amount": 100, "payment": 100, "data": {"walletId": "walletId"}, "verifiedAt": "2021-01-01 00:00:00", "createdAt": "2021-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Billing"], "summary": "Create user payment profile", "operationId": "b1af4dc6c2a158df9f74d5be3922fe89", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"type": "string"}, "data": {"type": "object"}, "paygateId": {"type": "string"}}, "type": "object"}, "example": {"name": "My Wallet", "data": {"walletId": "walletId"}, "paygateId": "paygateId"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "amount": {"type": "number"}, "payment": {"type": "number"}, "data": {"type": "object"}, "minUSD": {"type": "number"}, "maxUSD": {"type": "number"}, "verifiedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": "id", "name": "name", "amount": 100, "payment": 100, "data": {"walletId": "walletId"}, "minUSD": 100, "maxUSD": 100, "verifiedAt": "2021-01-01 00:00:00", "createdAt": "2021-01-01 00:00:00"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/profile/{id}": {"delete": {"tags": ["Billing"], "summary": "Delete user payment profile", "operationId": "********************************", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No Content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Billing"], "summary": "Edit user payment profile", "operationId": "5c139cda38429338f08196d0d40ff2ec", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"type": "string"}}, "type": "object"}, "example": {"name": "My Wallet"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/profile/{id}/confirm": {"put": {"tags": ["Billing"], "summary": "Confirm user payment profile, or resend confirmation email", "operationId": "0ae24c1243b41689327f12c243b530bd", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"maxProperties": 1, "minProperties": 1, "properties": {"token": {"type": "string"}, "resend": {"type": "boolean"}}, "type": "object"}, "example": {"token": "token"}}}}, "responses": {"201": {"description": "Confirmation sent"}, "204": {"description": "Profile Verified"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/billing/transaction": {"get": {"tags": ["Billing"], "summary": "Get user transaction", "operationId": "f1a3b224013a8dfc6d7a9777ec7492c3", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "User transaction", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "id", "invoiceId": "invoiceId", "currencyAmount": 100, "amount": 100, "currency": "USD", "transactionId": "transactionId", "createdAt": "2021-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/ads": {"get": {"tags": ["Platform"], "summary": "Current supported ad formats and providers", "operationId": "6927612803d125928f54725480a4604a", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"format": {"type": "array", "items": {"type": "string"}}, "provider": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "example": {"format": ["Onclick Ads", "Banner Ads", "Vast Tag", "<PERSON><PERSON>s"], "provider": ["Automatic", "Adsterra", "Mon<PERSON>g", "Clickadu"]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/announcement": {"get": {"tags": ["Platform"], "summary": "Get announcement list", "operationId": "649cb765bb095dd79102e0a32eefe130", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "subject": {"type": "string"}, "description": {"type": "string"}}, "type": "object"}}, "example": [{"id": 1, "subject": "Subject 1", "description": "Description 1"}, {"id": 2, "subject": "Subject 2", "description": "Description 2"}]}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/announcement/{id}": {"get": {"tags": ["Platform"], "summary": "Get announcement detail", "operationId": "2e46d5a4c703632256b341873c7efd02", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "subject": {"type": "string"}, "description": {"type": "string"}, "message": {"type": "string"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": 1, "subject": "Subject 1", "description": "Description 1", "message": "Message 1", "status": "Active", "updatedAt": "2024-01-01 00:00:00", "createdAt": "2024-01-01 00:00:00"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/browser": {"get": {"tags": ["Platform"], "summary": "Get browser list", "operationId": "e0338036ddda054e36529e910fabf39b", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "osId": {"type": "string"}, "name": {"type": "string"}}, "type": "object"}}, "example": [{"id": 1, "osId": 1, "name": "Chrome"}, {"id": 2, "osId": 1, "name": "Firefox"}, {"id": 3, "osId": 1, "name": "Safari"}, {"id": 4, "osId": 1, "name": "Edge"}]}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/country": {"get": {"tags": ["Platform"], "summary": "Get country list", "operationId": "b4f26237fc533dd4009f2913a5bb9cf2", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"name": {"type": "string"}, "code": {"type": "string"}, "phonePrefix": {"type": "string"}}, "type": "object"}}, "example": [{"name": "United States", "code": "US", "phonePrefix": "+1"}, {"name": "United Kingdom", "code": "UK", "phonePrefix": "+44"}, {"name": "Australia", "code": "AU", "phonePrefix": "+61"}]}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/language": {"get": {"tags": ["Platform"], "summary": "Get languages list", "operationId": "d1ebc8de1f59cb16ed2be01abd355dbb", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"code": {"type": "string"}, "name": {"type": "string"}, "nativeName": {"type": "string"}}, "type": "object"}}, "example": [{"code": "ab", "name": "Abkhaz", "nativeName": "аҧсуа"}, {"code": "aa", "name": "Afar", "nativeName": "<PERSON><PERSON><PERSON>"}, {"code": "af", "name": "Afrikaans", "nativeName": "Afrikaans"}, {"code": "ak", "name": "<PERSON><PERSON>", "nativeName": "<PERSON><PERSON>"}, {"code": "sq", "name": "Albanian", "nativeName": "Shqip"}, {"code": "am", "name": "Amharic", "nativeName": "አማርኛ"}, {"code": "ar", "name": "Arabic", "nativeName": "العربية"}, {"code": "an", "name": "Aragonese", "nativeName": "Aragonés"}, {"code": "hy", "name": "Armenian", "nativeName": "Հայերեն"}, {"code": "as", "name": "Assamese", "nativeName": "অসমীয়া"}, {"code": "av", "name": "<PERSON><PERSON>", "nativeName": "авар мацӀ, магӀарул мацӀ"}, {"code": "ae", "name": "Avestan", "nativeName": "a<PERSON>ta"}, {"code": "ay", "name": "Aymara", "nativeName": "aymar aru"}, {"code": "az", "name": "Azerbaijani", "nativeName": "a<PERSON>ərbaycan dili"}, {"code": "bm", "name": "Bambara", "nativeName": "b<PERSON><PERSON><PERSON>"}, {"code": "ba", "name": "Bashkir", "nativeName": "башҡорт теле"}]}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/os": {"get": {"tags": ["Platform"], "summary": "Get opera system list", "operationId": "85a0576cdf7cfbbf405016643bde9f4e", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "platformId": {"type": "string"}, "name": {"type": "string"}}, "type": "object"}}, "example": [{"id": 1, "platformId": 1, "name": "Windows"}, {"id": 2, "platformId": 1, "name": "MacOS"}, {"id": 3, "platformId": 1, "name": "Linux"}, {"id": 4, "platformId": 1, "name": "iOS"}, {"id": 5, "platformId": 1, "name": "Android"}]}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/platform/platform": {"get": {"tags": ["Platform"], "summary": "Get device platform list", "operationId": "59e4586dc2b47fc93e4b0150a3b97bbe", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "type": "object"}}, "example": [{"id": 1, "name": "Desktop"}, {"id": 2, "name": "Mobile"}, {"id": 3, "name": "Tablet"}]}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/referral/commission": {"get": {"tags": ["Referral"], "summary": "Get user referral commission", "operationId": "2968707acf386f04a1ba4dff48c75b53", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"invitedUser": "user_name", "commissionRate": "rate", "commissionAmount": "usd_amount", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/referral/signup": {"get": {"tags": ["Referral"], "summary": "Get user referral signup", "operationId": "82a6f4b73e97c2e59b800e052ef77d46", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"status": "status", "invitedUser": "user_name", "utmSource": "utm_source", "utmMedium": "utm_medium", "utmCampaign": "utm_campaign", "commissionCount": "count", "commissionAmount": "usd_amount", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/referral": {"get": {"tags": ["Referral"], "summary": "Get user referral summary", "operationId": "e00dd4b71eccc23798765466915c072c", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "service": {"type": "string"}, "available": {"type": "number"}, "pending": {"type": "number"}, "paidAmount": {"type": "number"}, "link": {"type": "string"}}, "type": "object"}, "example": {"id": "jgygg", "service": "Referral", "available": 0, "pending": 0, "paidAmount": 0, "link": "https://streamp2p.com/api/invite/12345678"}}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/support/ticket": {"get": {"tags": ["Support"], "summary": "Get user support tickets", "operationId": "859a87343e68bc6277e16e74c7728f5b", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "ticket_id", "subject": "Ticket subject", "status": "Open", "escalatedAt": null, "updatedAt": "2024-01-01 00:00:00", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Support"], "summary": "Create a support ticket", "operationId": "468512651276bdb3f04b75d58c29e6bd", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"subject": {"type": "string"}, "message": {"type": "string"}}, "type": "object"}, "example": {"subject": "Ticket subject", "message": "Ticket message"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "ticket_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/support/ticket/{id}": {"get": {"tags": ["Support"], "summary": "Get user support ticket", "operationId": "69527960c5b30445f0e0d8f597594f4b", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "subject": {"type": "string"}, "status": {"type": "string"}, "escalatedAt": {"type": ["string", "null"]}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "messages": {"type": "array", "items": {"properties": {"userName": {"type": "string"}, "message": {"type": "string"}, "avatar": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}}}, "type": "object"}, "example": {"id": "ticket_id", "subject": "Ticket subject", "status": "Open", "escalatedAt": null, "updatedAt": "2024-01-01 00:00:00", "createdAt": "2024-01-01 00:00:00", "messages": [{"userName": "userName", "message": "Message content", "avatar": "https://example.com/avatar.jpg", "createdAt": "2024-01-01 00:00:00"}]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Support"], "summary": "Create a message for a support ticket", "operationId": "fb1b54e28f8ef78b01b24f7117af38cc", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}, "example": {"message": "Your message here"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "ticket_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/support/ticket/{id}/close": {"put": {"tags": ["Support"], "summary": "Close a support ticket", "operationId": "6226c0b2384d39bb4b5fce7e3d2586df", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/team": {"get": {"tags": ["Team"], "summary": "Get all teams that you are leader or member and all permissions", "operationId": "c8831122766b1dfb2cc458dd5e708ae1", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {}, "example": {"leader": {"id": "team_id", "name": "team_name", "description": "team_description", "createdAt": "2024-01-01 00:00:00"}, "member": [{"id": "team_id", "name": "team_name", "description": "team_description", "createdAt": "2024-01-01 00:00:00"}], "permissions": ["read", "write", "delete"]}}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Team"], "summary": "Create a new team", "operationId": "328b808ad6d7c152acc81306f6b02584", "requestBody": {"required": true, "content": {"application/json": {"schema": {}, "example": {"name": "team_name", "description": "team_description"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {}, "example": {"id": "team_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/team/{id}": {"get": {"tags": ["Team"], "summary": "Get a team by ID", "operationId": "e125ec296d4f9f7c456de9af005be5e5", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {}, "example": {"id": "team_id", "name": "team_name", "description": "team_description", "createdAt": "2024-01-01 00:00:00", "members": [{"email": "member_email", "name": "member_name", "avatar": "member_avatar", "permission": ["read", "write", "delete"]}], "permissions": ["read", "write", "delete"]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Team"], "summary": "Delete a team by ID", "operationId": "d7fa1de1de2bf34223e091343138ebfd", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "Deleted"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Team"], "summary": "Update a team", "operationId": "8b44134e81d42a54f15913f307b1d2d6", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {}, "example": {"name": "team_name", "description": "team_description"}}}}, "responses": {"204": {"description": "Updated"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/team/{id}/invite": {"post": {"tags": ["Team"], "summary": "Invite user to join your team", "operationId": "8ffca28fc0c9f8c13f3565e3ab88bc32", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {}, "example": {"email": "user_email", "permission": ["read", "write", "delete"]}}}}, "responses": {"204": {"description": "Invited"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/team/{id}/member": {"delete": {"tags": ["Team"], "summary": "Reject a member of team that you are leader", "operationId": "543e4a495a90b12069ec4db44c49adb8", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {}, "example": {"member": "member_email"}}}}, "responses": {"204": {"description": "Rejected"}, "401": {"$ref": "#/components/responses/401"}, "400": {"$ref": "#/components/responses/400"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Team"], "summary": "Update member permission of team that you are leader", "operationId": "869aa0b4fe9d3dcd8a3d17fd397c0d30", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {}, "example": {"member": "user_email", "permission": ["read", "write", "delete"]}}}}, "responses": {"204": {"description": "Updated"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/team/{id}/activity": {"get": {"tags": ["Team"], "summary": "Get team activity, only for leader", "operationId": "8e8b50c53ca8114838ee313572c0b4bd", "parameters": [{"$ref": "#/components/parameters/id"}, {"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"member": "member_name", "service": "service_name", "ipAddress": "ip_address", "device": "device", "country": "country", "message": "message", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/team/{id}/invitation": {"delete": {"tags": ["Team"], "summary": "Reject an invitation", "operationId": "117797e893ac33d720a5233e286d72ff", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "Rejected"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Team"], "summary": "Accept an invitation", "operationId": "72b99be419f507039fba80c385a0f711", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "Accepted"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/account-manager": {"get": {"tags": ["User"], "summary": "Get user's account manager", "operationId": "d7e8e9430d65a10b79b6c9a2acd8d1c4", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"avatar": {"type": "string"}, "name": {"type": "string"}, "title": {"type": "string"}, "channels": {"type": "array", "items": {"properties": {"name": {"type": "string"}, "account": {"type": "string"}, "url": {"type": "string"}}, "type": "object"}}}, "type": "object"}, "example": {"avatar": "https://example.com/avatar.jpg", "name": "<PERSON>", "title": "Account Manager", "channels": [{"name": "Facebook", "account": "john.doe", "url": "https://facebook.com/john.doe"}, {"name": "Twitter", "account": "john_doe", "url": "https://twitter.com/john_doe"}]}}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/activity": {"get": {"tags": ["User"], "summary": "Get user activity", "operationId": "0f97902daab7db967c1bdd56fcf9e2d2", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"type": "type", "ipAddress": "ip_address", "device": "device", "country": "country", "message": "message", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user": {"delete": {"tags": ["User"], "summary": "Delete user account", "operationId": "8447217a8195484965a2446f76f9b694", "responses": {"204": {"description": "No content"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/email": {"get": {"tags": ["User"], "summary": "Get user email", "operationId": "7be91f8c3ab8526e20d552e878b1cdf7", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"type": "type", "subject": "subject", "token": "email_token", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/file": {"get": {"tags": ["User"], "summary": "Get user uploaded files", "operationId": "8895367fa2148f15e6577430850ecc31", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "file_id", "storageId": "storage_id", "name": "file_name", "path": "file_path", "url": "https://example.com/image.png", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "put": {"tags": ["User"], "summary": "Upload image file", "operationId": "3d2bde158ed5bfc3963e4b4ae9e2cffb", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"file": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}}, "type": "object"}, "example": {"url": "https://example.com/image.png"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/information": {"get": {"tags": ["User"], "summary": "Get user information", "operationId": "d3459e35d35f79f53545645ef260005f", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "number"}, "name": {"type": "string"}, "email": {"type": "string"}, "address": {"type": ["string", "null"]}, "country": {"type": "string"}, "timezone": {"type": "string"}, "phone": {"type": ["string", "null"]}, "organization": {"type": ["string", "null"]}, "createdAt": {"type": "string"}, "updatedAt": {"type": ["string", "null"]}}, "type": "object"}, "example": {"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "address": "123 Main St", "country": "US", "timezone": "America/New_York", "phone": "+1234567890", "organization": "Faker Company", "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00"}}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["User"], "summary": "Update user information", "operationId": "235b0741af604a6e8ac7a415eeeead78", "requestBody": {"required": true, "content": {"application/json": {"schema": {"minProperties": 1, "properties": {"name": {"type": "string"}, "address": {"type": ["string", "null"]}, "country": {"type": "string"}, "timezone": {"type": "string"}, "phone": {"type": ["string", "null"]}, "organization": {"type": ["string", "null"]}}, "type": "object"}, "example": {"name": "<PERSON>", "address": "123 Main St", "country": "US", "timezone": "America/New_York", "phone": "+1234567890", "organization": "Faker Company"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/notification": {"get": {"tags": ["User"], "summary": "Get user notification", "operationId": "c56f8bc231cb4fdfb5e1ad48c24815ad", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"name": "type", "in": "query", "description": "Type of notification", "required": false, "schema": {"type": "string", "enum": ["unread", "read"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "notification_id", "status": "status", "title": "title", "description": "description", "readAt": "2024-01-01 00:00:00", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "put": {"tags": ["User"], "summary": "Mark all notifications as read", "operationId": "fc1b02120ce9730e44c1d80271a3045f", "responses": {"204": {"description": "No Content"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/notification/{id}": {"patch": {"tags": ["User"], "summary": "Mark notification as read", "operationId": "2f8f18586b7b6405eb5af6a02760055f", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No Content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/preference": {"get": {"tags": ["User"], "summary": "Get user preference", "operationId": "ace9d1daf9ddde5b07955c4d5e750417", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"newsletter": {"type": "boolean"}, "language": {"type": "string", "enum": ["English", "Español", "मानक हिन्दी", "Le Français", "Tiếng <PERSON>"]}, "theme": {"type": "string", "enum": ["light", "dark"]}, "direction": {"type": "string", "enum": ["rtl", "ltr"]}}, "type": "object"}, "example": {"newsletter": true, "language": "en", "theme": "light", "direction": "ltr"}}}}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["User"], "summary": "Update user preference", "operationId": "3fe698ecb151e0b2486c0f97232f4bfe", "requestBody": {"required": true, "content": {"application/json": {"schema": {"minProperties": 1, "properties": {"newsletter": {"type": "boolean"}, "language": {"type": "string", "enum": ["English", "Español", "मानक हिन्दी", "Le Français", "Tiếng <PERSON>"]}, "theme": {"type": "string", "enum": ["light", "dark"]}, "direction": {"type": "string", "enum": ["rtl", "ltr"]}, "displayMode": {"type": "string", "enum": ["grid", "table"]}, "resultPerPage": {"type": "integer", "maximum": 200, "minimum": 1}}, "type": "object"}, "example": {"newsletter": true, "language": "en", "theme": "light", "direction": "ltr", "displayMode": "grid", "resultPerPage": 20}}}}, "responses": {"204": {"description": "Preference updated"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/session": {"get": {"tags": ["User"], "summary": "Get user session", "operationId": "fe52f1899577bea42bc95feb124b294d", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "session_id", "status": "Active", "ipAddress": "ip", "device": "device", "country": "country", "isRemembered": true, "createdAt": "2024-01-01 00:00:00", "updatedAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["User"], "summary": "Terminate all active sessions", "operationId": "7204741dbe2f2f15748b812c4ea87317", "responses": {"204": {"description": "Session destroyed"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/session/{id}": {"delete": {"tags": ["User"], "summary": "Terminate user session by ID", "operationId": "111a983ee822d33376541528a0a1153f", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "Session destroyed"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/user/session/remembered": {"delete": {"tags": ["User"], "summary": "Terminate all remembered sessions", "operationId": "10d33bcca709dd45e585d5a72b122d66", "responses": {"204": {"description": "Session destroyed"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/advance-upload": {"get": {"tags": ["Video"], "summary": "Get advanced uploading tasks list", "description": "Please note system will remove Completed tasks information after 3 days, to keep the system clean", "operationId": "634afecef1317b142435043107f27646", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "abc", "name": "file.torrent", "status": "Queued", "videos": [], "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Video"], "summary": "Create advanced uploading task", "description": "If successful, the response will contain the task ID, you can use the task detail API to check the status and videos created from the task", "operationId": "0d599108be6830e23d2ab3c2fa2dd8e5", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}, "name": {"type": ["string", "null"]}, "selected": {"description": "Use this to select specific videos when the source contains multiple videos (e.g. torrent)", "type": ["array", "null"], "items": {"type": "string"}, "maxItems": 10}, "folderId": {"description": "Folder ID to save the videos to, if not provided, the default folder will be used", "type": ["string", "null"]}}, "type": "object"}, "example": {"url": "https://acb.com/file.mp4", "name": "An example video"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "task_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/advance-upload/{id}": {"get": {"tags": ["Video"], "summary": "Get upload task detail", "operationId": "1975618c648a0ae3517239f6af7868a7", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string"}, "error": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": "abc", "name": "magnet:.......", "status": "Completed", "videos": ["videoId1", "videoId2"], "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete advance upload task", "operationId": "0bd553e37918157ede04e41fc5fc913d", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video": {"get": {"tags": ["Video"], "summary": "Get video summary", "operationId": "1b34087c047429a95cf0c2dbb47f3bff", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}, {"$ref": "#/components/parameters/status"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"video": {"type": "integer"}, "player": {"type": "integer"}, "store": {"type": "integer"}, "impression": {"type": "integer"}, "play": {"type": "integer"}, "premiumPlay": {"type": "integer"}, "download": {"type": "integer"}, "premiumDownload": {"type": "integer"}, "error": {"type": "integer"}}, "type": "object"}, "example": {"video": 100000, "player": 10, "store": 123456789, "impression": 0, "play": 0, "premiumPlay": 0, "download": 0, "premiumDownload": 0, "error": 0}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/realtime": {"get": {"tags": ["Video"], "summary": "Get realtime playing", "operationId": "bd55b9ff28dac0a88d33349b722c61ae", "parameters": [{"name": "top", "in": "query", "description": "Which top to get, default is country", "required": false, "schema": {"type": "string", "enum": ["videoId", "country", "playerId"]}}, {"name": "metric", "in": "query", "description": "Which metric to get, default is play", "required": false, "schema": {"type": "string", "enum": ["play", "download"]}}, {"name": "limit", "in": "query", "description": "Limit of result, default is 5", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 5}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"total": {"type": "integer"}, "data": {"type": "array", "items": {"type": "object"}}}, "type": "object"}}, "example": {"total": 150, "data": [{"country": "JP", "play": 100}, {"country": "US", "play": 50}]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/dmca": {"get": {"tags": ["Video"], "summary": "Get dmca report list", "operationId": "865ea1762a9a09f4a947d0b9b222df90", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "abc", "videoId": "abc", "subject": "dmca subject", "description": "dmca description", "originUrl": "https://example.com", "proofOfCopyright": "proof", "statements": "statements", "status": "Pending", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/dmca/{id}": {"get": {"tags": ["Video"], "summary": "Get dmca report detail", "operationId": "4f240401140e4853321a1bebefa3510f", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "videoId": {"type": "string"}, "subject": {"type": "string"}, "description": {"type": "string"}, "originUrl": {"type": "string"}, "proofOfCopyright": {"type": "string"}, "statements": {"type": "string"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}, "messages": {"type": "array", "items": {"type": "object"}}}, "type": "object"}, "example": {"id": "abc", "videoId": "abc", "subject": "dmca subject", "description": "dmca description", "originUrl": "https://example.com", "proofOfCopyright": "proof", "statements": "statements", "status": "Pending", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00", "messages": [{"id": "abc", "message": "message", "createdAt": "2025-01-01 00:00:00", "user": {"name": "name", "email": "email", "avatar": "avatar"}}]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/dmca/{id}/message": {"post": {"tags": ["Support"], "summary": "Send a message to a dmca case", "operationId": "eab8791b2ac987a8b62e2144088791ae", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "type": "object"}, "example": {"message": "Your message here"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "dmca_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/dmca/{id}/resolve": {"put": {"tags": ["Support"], "summary": "Automatic resolve a dmca case", "operationId": "8a72c9ec6238318a59010787f696c816", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/folder": {"get": {"tags": ["Video"], "summary": "Get folder list", "operationId": "a234da0ebc04025609736684634448cd", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "parentId": {"type": "string"}, "folderCount": {"type": "integer"}, "videoCount": {"type": "integer"}, "isDefault": {"type": "boolean"}, "createdAt": {"type": "string"}}, "type": "object"}}, "example": [{"id": "folder_id", "name": "folder_name", "description": "folder_description", "parentId": "parent_id", "folderCount": 0, "videoCount": 0, "isDefault": false, "createdAt": "created_at"}]}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Video"], "summary": "Create new folder", "operationId": "da13de34fb2c6b5f088434d13b2faa6b", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"folderId": {"type": ["string", "null"]}, "name": {"type": "string"}, "description": {"type": ["string", "null"]}, "isDefault": {"type": ["boolean", "null"]}}, "type": "object"}, "example": {"folderId": "parent_folder_id", "name": "folder_name", "description": "folder_description"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "folder_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete multiple folders (NO delete contents)", "description": "This action will delete folders only, not the contents inside. The contents will be moved to the root folder.", "operationId": "c08dad67102d346e80cb2636092cffdb", "requestBody": {"required": true, "content": {"application/json": {"schema": {"maxProperties": 1, "minProperties": 1, "properties": {"ids": {"type": ["array", "null"], "items": {"type": "string"}}}, "type": "object"}, "example": {"ids": ["folder_id1", "folder_id2"]}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/folder/{id}": {"get": {"tags": ["Video"], "summary": "List videos in folder", "operationId": "cb793ff06f5f524fdcce2049901f32ed", "parameters": [{"$ref": "#/components/parameters/id"}, {"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "video_id", "name": "video_name", "poster": "poster_url", "size": 0, "width": 0, "height": 0, "type": "MKV", "status": "status", "createdAt": "created_at"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete folder (NO delete contents)", "description": "This action will delete the folder only, not the contents inside. The contents will be moved to the root folder.", "operationId": "5f9dd4f024c8f48ebabe278c86a19832", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Video"], "summary": "update folder name & description, move to another folder", "operationId": "ddcc86a02780e53e9825f6b3f2b95200", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"minProperties": 1, "properties": {"folderId": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "isDefault": {"type": ["boolean", "null"]}, "position": {"type": ["integer", "null"]}}, "type": "object"}, "example": {"name": "new name"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/folder/{id}/link": {"post": {"tags": ["Video"], "summary": "Link (add or move) video to folder", "operationId": "418fa6f8d88a239f1f4c046bd45344a8", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"maxProperties": 1, "minProperties": 1, "properties": {"videoId": {"type": ["string", "null"]}, "position": {"type": ["integer", "null"]}, "videoIds": {"type": ["array", "null"], "items": {"properties": {"id": {"type": "string"}, "position": {"type": ["integer", "null"]}}, "type": "object"}}}, "type": "object"}, "example": {"videoId": "video_id"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/folder/{id}/unlink": {"post": {"tags": ["Video"], "summary": "Unlink video from folder", "operationId": "676d0d4d709e9eae962b45e28e90ccd8", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"maxProperties": 1, "minProperties": 1, "properties": {"videoId": {"type": ["string", "null"]}, "videoIds": {"type": ["array", "null"], "items": {"type": "string"}}}, "type": "object"}, "example": {"videoId": "video_id"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/live": {"get": {"tags": ["Video"], "summary": "Get endpoint to broadcast live video", "operationId": "e182e734e5986265f7b56a1112394f69", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"endpoint": {"type": "string"}}, "type": "object"}, "example": {"endpoint": "rtmp://liveserver.com:1935/live/123456?token=eyJpZCI"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/manage": {"get": {"tags": ["Video"], "summary": "Get video list", "operationId": "626efd1c2cedb678bbaab1eb03ecdd54", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "video_id", "name": "video_name", "poster": "poster_url", "subtitle": 0, "impression": 0, "play": 0, "premiumPlay": 0, "download": 0, "premiumDownload": 0, "size": 0, "duration": 0, "width": 0, "height": 0, "resolution": "resolution", "bitrate": 0, "framerate": 0, "codec": "codec", "audioBitrate": 0, "audioChannels": 0, "audioSampleRate": 0, "audioCodec": "audio_codec", "status": "status", "updatedAt": "updated_at", "createdAt": "created_at"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Video"], "summary": "Clone video", "operationId": "68a338a52f4eb7a43924349a2cf860ba", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"videoId": {"type": "string"}, "name": {"type": "string"}}, "type": "object"}, "example": {"videoId": "videoId", "name": "New name"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "video_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete multiple videos", "operationId": "0c358764a6abe701ebd80292b13bbc21", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"ids": {"type": "array", "items": {"type": "string"}}}, "type": "object"}, "example": {"ids": ["video_id1", "video_id2"]}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/manage/{id}": {"get": {"tags": ["Video"], "summary": "Get video detail", "operationId": "14afdb5b007b81bd93aefd6da49e894e", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "poster": {"type": "string"}, "subtitle": {"type": "integer"}, "impression": {"type": "integer"}, "play": {"type": "integer"}, "premiumPlay": {"type": "integer"}, "download": {"type": "integer"}, "premiumDownload": {"type": "integer"}, "size": {"type": "integer"}, "duration": {"type": "integer"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "resolution": {"type": "string"}, "bitrate": {"type": "integer"}, "framerate": {"type": "integer"}, "codec": {"type": "string"}, "audioBitrate": {"type": "integer"}, "audioChannels": {"type": "integer"}, "audioSampleRate": {"type": "integer"}, "audioCodec": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": "video_id", "name": "video_name", "poster": "poster_url", "subtitle": 0, "impression": 0, "play": 0, "premiumPlay": 0, "download": 0, "premiumDownload": 0, "size": 0, "duration": 0, "width": 0, "height": 0, "resolution": "resolution", "bitrate": 0, "framerate": 0, "codec": "codec", "audioBitrate": 0, "audioChannels": 0, "audioSampleRate": 0, "audioCodec": "audio_codec", "type": "type", "status": "status", "updatedAt": "updated_at", "createdAt": "created_at"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete video", "operationId": "ff8db0687b1fc8170a9f17915935f5c3", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Video"], "summary": "Rename video", "operationId": "bd6436a0e81cde0d0433e7007217cff8", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"type": "string"}}, "type": "object"}, "example": {"name": "new name"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/manage/{id}/files": {"get": {"tags": ["Video"], "summary": "Get video related files & tasks", "operationId": "dfb77b1b178a47cd53f0ca7bf1288228", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}}, "example": [{"id": "file_id", "type": "subtitle", "name": "EN", "createdAt": "created_at"}]}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/manage/{id}/subtitle": {"put": {"tags": ["Video"], "summary": "Upload video subtitle (vtt, srt, ass)", "operationId": "9d193617fc031558ccb6ffbda7923710", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"language": {"type": "string", "maxLength": 2, "minLength": 2}, "name": {"type": ["string", "null"]}, "file": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}}, "type": "object"}, "example": {"url": "https://example.com/subtitle.vtt"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/manage/{id}/poster": {"put": {"tags": ["Video"], "summary": "Upload custom poster (png, jpg)", "operationId": "e9d4a50e041270521516fd6bcba7a860", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"file": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}}, "type": "object"}, "example": {"url": "https://example.com/image.png"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Video"], "summary": "Set scene as custom poster", "operationId": "75b535eee04da272f5bf0b22df543fad", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"atMillisecond": {"type": "integer"}, "width": {"type": ["integer", "null"]}, "height": {"type": ["integer", "null"]}}, "type": "object"}, "example": {"atMillisecond": 100000, "width": 1920, "height": 1080}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}}, "type": "object"}, "example": {"url": "https://example.com/image.png"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/manage/{id}/subtitle/{subtitleId}": {"delete": {"tags": ["Video"], "summary": "Delete video subtitle", "operationId": "02b296db82ce7c1e6e3bfbf366e7a771", "parameters": [{"$ref": "#/components/parameters/id"}, {"name": "subtitleId", "in": "path", "description": "Subtitle id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Video"], "summary": "Edit video subtitle", "operationId": "199a39d751690682f21ace86116d6ae2", "parameters": [{"$ref": "#/components/parameters/id"}, {"name": "subtitleId", "in": "path", "description": "Subtitle id", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"language": {"type": "string"}, "name": {"type": "string"}}, "type": "object"}, "example": {"language": "es", "name": "Spanish"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player": {"get": {"tags": ["Video"], "summary": "Get player list", "operationId": "b169dd416979d8cd0f2e52d502520175", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}, {"$ref": "#/components/parameters/status"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "player_id", "domain": "domain", "logo": "logo", "isPremium": true, "allowDownload": true, "isDefault": true, "impression": 0, "play": 0, "premiumPlay": 0, "download": 0, "premiumDownload": 0, "configuration": "configuration", "status": "status", "updatedAt": "2024-01-01 00:00:00", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Video"], "summary": "Create player", "operationId": "f31251e6950db582c19645fcaa84ff22", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"domain": {"type": "string"}}, "type": "object"}, "example": {"domain": "domain"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "player_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/default": {"get": {"tags": ["Video"], "summary": "Get default player", "operationId": "eddc850b3439276d5d4c8ec266dd4470", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"domain": {"type": "string"}, "embedWidth": {"type": "integer"}, "embedHeight": {"type": "integer"}}, "type": "object"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/domain": {"get": {"tags": ["Video"], "summary": "Get list of free subdomains for player", "operationId": "f5c04836e934963f292fe3c15add6790", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}, "example": ["domain1.com", "domain2.com"]}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/{id}": {"get": {"tags": ["Video"], "summary": "Get player", "operationId": "a0d2d11555e3845883d0e60c1e0db42d", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "domain": {"type": "string"}, "logo": {"type": "string"}, "isPremium": {"type": "boolean"}, "allowDownload": {"type": "boolean"}, "isDefault": {"type": "boolean"}, "impression": {"type": "integer"}, "play": {"type": "integer"}, "premiumPlay": {"type": "integer"}, "download": {"type": "integer"}, "premiumDownload": {"type": "integer"}, "configuration": {"type": "object"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete player", "operationId": "cb26a2738c6c3bc8618413e2403c5d22", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Video"], "summary": "Update video player", "operationId": "1c8bcfb89f36265c39b279ef55fc8a87", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"minProperties": 1, "properties": {"isDefault": {"type": "boolean"}, "isPremium": {"type": "boolean"}, "allowDownload": {"type": "boolean"}, "allowErotic": {"type": "boolean"}, "allowAdblock": {"type": "boolean"}, "allowExternal": {"type": "boolean"}, "iframeApi": {"type": "boolean"}, "pickSubtitle": {"type": "boolean"}, "embedWidth": {"type": "integer"}, "embedHeight": {"type": "integer"}, "ui": {"type": "object"}, "translation": {"type": "object"}, "ads": {"type": "array", "items": {"properties": {"format": {"type": "string", "enum": ["Onclick Ads", "Banner Ads", "Vast Tag", "<PERSON><PERSON>s"]}, "provider": {"type": "string", "enum": ["Automatic", "Adsterra", "Clickadu", "Mon<PERSON>g", "Other"]}, "status": {"type": "string", "enum": ["Active", "Inactive"]}, "content": {"type": ["string", "null"], "format": "jsUrlOrCode"}, "start": {"type": "string", "format": "duration"}}, "type": "object"}}, "restrictEmbed": {"type": "array", "items": {"type": "string", "format": "domain"}}, "restrictCountry": {"type": "array", "items": {"type": "string", "format": "CountryCode"}}, "logo": {"minProperties": 1, "properties": {"position": {"type": "string", "enum": ["top-left", "top-right", "bottom-left", "bottom-right", "control-bar", "hidden"]}, "url": {"type": "string", "format": "uri"}, "asset": {"type": "string", "format": "base64"}}, "type": "object"}}, "type": "object"}, "example": {"allowDownload": true}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "402": {"$ref": "#/components/responses/402"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/{id}/realtime": {"get": {"tags": ["Video"], "summary": "Get player's realtime playing/downloading", "operationId": "bba3c7a1c569ef2e3eb0d18cc2cd47fd", "parameters": [{"$ref": "#/components/parameters/id"}, {"name": "top", "in": "query", "description": "Which top to get, default is country", "required": false, "schema": {"type": "string", "enum": ["videoId", "country"]}}, {"name": "metric", "in": "query", "description": "Which metric to get, default is play", "required": false, "schema": {"type": "string", "enum": ["play", "download"]}}, {"name": "limit", "in": "query", "description": "Limit of result, default is 5", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 5}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"total": {"type": "integer"}, "data": {"type": "array", "items": {"type": "object"}}}, "type": "object"}}, "example": {"total": 150, "data": [{"country": "JP", "play": 100}, {"country": "US", "play": 50}]}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/{id}/activate": {"put": {"tags": ["Video"], "summary": "Activate player", "operationId": "11c8338a5d873d9c21e45684a3957a80", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/ads": {"get": {"tags": ["Video"], "summary": "Get all player ads list", "operationId": "55a982afc829aa9d575619356b44f9dc", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}, {"$ref": "#/components/parameters/status"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "ads_id", "playerId": "player_id", "playerDomain": "domain", "impression": 0, "click": 0, "revenue": 0, "format": "format", "startTime": "00:00:00", "provider": "provider", "status": "status", "updatedAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/{id}/ads": {"get": {"tags": ["Video"], "summary": "Get ads list by player", "operationId": "0f1819a6da6d08365cdb7c30944195ae", "parameters": [{"$ref": "#/components/parameters/id"}, {"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/search"}, {"$ref": "#/components/parameters/status"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "ads_id", "impression": 0, "click": 0, "revenue": 0, "format": "format", "startTime": "00:00:00", "provider": "provider", "status": "status", "content": "content", "updatedAt": "2024-01-01 00:00:00", "createdAt": "2024-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}, "post": {"tags": ["Video"], "summary": "Create player ads", "operationId": "b40f117397cc68bf8740f906593e0793", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"format": {"type": "string"}, "provider": {"type": "string"}, "status": {"type": "string", "enum": ["Active", "Inactive"]}, "content": {"type": ["string", "null"], "format": "jsUrlOrCode"}, "zoneId": {"type": ["string", "null"]}, "apiToken": {"type": ["string", "null"]}, "startTime": {"type": "string", "format": "duration"}}, "type": "object"}, "example": {"format": "Onclick Ads", "provider": "Automatic", "status": "Active", "content": null, "startTime": "00:00:00"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}}, "type": "object"}, "example": {"id": "ads_id"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/player/ads/{id}": {"get": {"tags": ["Video"], "summary": "Get ad detail", "operationId": "6196983e67a044a48bfd11e881c055a8", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "format": {"type": "string"}, "provider": {"type": "string"}, "startTime": {"type": "string"}, "content": {"type": "string"}, "impression": {"type": "integer"}, "click": {"type": "integer"}, "revenue": {"type": "number", "format": "double"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete player ads", "operationId": "7fb2b45fae6f14e8a81b524b2e6259f3", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}, "patch": {"tags": ["Video"], "summary": "Update player ads", "operationId": "027635e3f8f656ebb00597d36a00663d", "parameters": [{"$ref": "#/components/parameters/id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"minProperties": 1, "properties": {"status": {"type": "string", "enum": ["Active", "Inactive"]}, "content": {"type": ["string", "null"], "format": "jsUrlOrCode"}, "zoneId": {"type": ["string", "null"]}, "apiToken": {"type": ["string", "null"]}, "startTime": {"type": "string", "format": "duration"}}, "type": "object"}, "example": {"startTime": "00:00:00"}}}}, "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "402": {"$ref": "#/components/responses/402"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/report": {"get": {"tags": ["Video"], "summary": "Get video report", "operationId": "602205755a87f82bf12d51d1e3a797ea", "parameters": [{"name": "date", "in": "query", "description": "report date, for single date report", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "startDate", "in": "query", "description": "report start date, for date range report", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "report end date, for date range report", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "playerId", "in": "query", "description": "filter by player id", "required": false, "schema": {"type": "string"}}, {"name": "videoId", "in": "query", "description": "filter by video id", "required": false, "schema": {"type": "string"}}, {"name": "adId", "in": "query", "description": "filter by ad id", "required": false, "schema": {"type": "string"}}, {"name": "country", "in": "query", "description": "filter by country", "required": false, "schema": {"type": "string"}}, {"name": "platform", "in": "query", "description": "filter by platform", "required": false, "schema": {"type": "string"}}, {"name": "os", "in": "query", "description": "filter by os", "required": false, "schema": {"type": "string"}}, {"name": "browser", "in": "query", "description": "filter by browser", "required": false, "schema": {"type": "string"}}, {"name": "groupBy", "in": "query", "description": "group by field", "required": false, "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "description": "sort by field, eg: play (same as play:DESC), play:ASC", "required": false, "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "should be used with sortBy only, to get top N rows", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}, "example": [{"date": "2025-01-01", "videoId": "videoId", "playerId": "playerId", "country": "NL", "platform": "Desktop", "os": "Windows", "browser": "Chrome", "play": 100, "premiumPlay": 50, "download": 1000, "PremiumDownload": 10}]}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/subtitle": {"get": {"tags": ["Video"], "summary": "Get subtitle exporting tasks list", "operationId": "c3a8c1163fe99a57840856adb3343703", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "abc", "videoId": "abc", "streamIndex": 12, "subtitleCodec": "srt", "language": "eng", "status": "Queued", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/subtitle/{id}": {"get": {"tags": ["Video"], "summary": "Get subtitle task detail", "operationId": "15245e0a41745b0c2634cf1d6a524ea3", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "videoId": {"type": "string"}, "streamIndex": {"type": "integer"}, "subtitleCodec": {"type": "string"}, "language": {"type": "string"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": "abc", "videoId": "abc", "streamIndex": 12, "subtitleCodec": "srt", "language": "eng", "status": "Queued", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete failed subtitle task", "operationId": "6501deba7448e1a6863d41e4ab3f41a6", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/thumbnail": {"get": {"tags": ["Video"], "summary": "Get thumbnail & poster generating tasks list", "operationId": "219ec288857aab4d95fd08940c7c71b3", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "abc", "videoId": "abc", "posterWidth": 1920, "posterHeight": 1080, "thumbWidth": 1000, "thumbHeight": 30, "thumbTotal": 128, "thumbEvery": 2, "spriteWidth": 44100, "status": "Queued", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/thumbnail/{id}": {"get": {"tags": ["Video"], "summary": "Get thumbnail task detail", "operationId": "d69e893147e6c0c6a80b64c538419f33", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "videoId": {"type": "string"}, "posterWidth": {"type": "integer"}, "posterHeight": {"type": "integer"}, "thumbWidth": {"type": "integer"}, "thumbHeight": {"type": "integer"}, "thumbTotal": {"type": "integer"}, "thumbEvery": {"type": "integer"}, "spriteWidth": {"type": "integer"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": "abc", "videoId": "abc", "posterWidth": 1920, "posterHeight": 1080, "thumbWidth": 1000, "thumbHeight": 30, "thumbTotal": 128, "thumbEvery": 2, "spriteWidth": 44100, "status": "Queued", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete failed thumbnail task", "operationId": "d6758c4db2c4b7640a8d91547ba0a8ad", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/transcode": {"get": {"tags": ["Video"], "summary": "Get transcoding tasks list", "operationId": "3711ad09a6c5e40cce1b295f28e5c97c", "parameters": [{"$ref": "#/components/parameters/page"}, {"$ref": "#/components/parameters/perPage"}, {"$ref": "#/components/parameters/status"}, {"$ref": "#/components/parameters/search"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"type": "object"}}, "metadata": {"$ref": "#/components/schemas/Pagination"}}, "type": "object"}, "example": {"data": [{"id": "abc", "videoId": "abc", "width": 1920, "height": 1080, "bitrate": 1000, "framerate": 30, "audioBitrate": 128, "audioChannels": 2, "audioSampleRate": 44100, "status": "Queued", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}], "metadata": {"currentPage": 1, "perPage": 10, "total": 100, "maxPage": 10, "offset": 0}}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/transcode/{id}": {"get": {"tags": ["Video"], "summary": "Get transcode task detail", "operationId": "dd71038c30bff2067d310ebb0e09e675", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "videoId": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "bitrate": {"type": "integer"}, "framerate": {"type": "integer"}, "audioBitrate": {"type": "integer"}, "audioChannels": {"type": "integer"}, "audioSampleRate": {"type": "integer"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}, "createdAt": {"type": "string"}}, "type": "object"}, "example": {"id": "abc", "videoId": "abc", "width": 1920, "height": 1080, "bitrate": 1000, "framerate": 30, "audioBitrate": 128, "audioChannels": 2, "audioSampleRate": 44100, "status": "Queued", "updatedAt": "2025-01-01 00:00:00", "createdAt": "2025-01-01 00:00:00"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "500": {"$ref": "#/components/responses/500"}}}, "delete": {"tags": ["Video"], "summary": "Delete failed transcoding task", "operationId": "185fb95d43c23c9da5a9b16b5ae6a24d", "parameters": [{"$ref": "#/components/parameters/id"}], "responses": {"204": {"description": "No content"}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "404": {"$ref": "#/components/responses/404"}, "409": {"$ref": "#/components/responses/409"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/upload": {"get": {"tags": ["Video"], "summary": "Get endpoints to upload video", "description": "We use tus protocol for uploading video. chunkSize should be 52,428,800 bytes. You can use any tus clients to upload video. Max file size is 20GB. Access token is valid for 2 hours only, but once uploading started, it will not be expired. Make sure \"accessToken\", \"filename\", \"filetype\", and optional \"folderId\" are passed in the \"metadata\" header when POST/PATCH-ing to tus endpoint", "operationId": "5de9570643bea9ce52bbb88f2c2cd76a", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"tusUrl": {"type": "string"}, "accessToken": {"type": "string"}}, "type": "object"}, "example": {"tusUrl": "https://upload.example.com/upload/", "accessToken": "eyJ0eXA"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}, "/api/v1/video/upload/ftp": {"get": {"tags": ["Video"], "summary": "Get ftp endpoints to upload video", "description": "FTP credentials are valid for 2 hours only. Please note this is for uploading only, not for storing or managing videos. Once the video is uploaded, it will be moved out of ftp server immediately.", "operationId": "d9d88234c1206b3d8727d30fafdd1618", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"properties": {"ftpUrl": {"type": "string"}, "ftpUser": {"type": "string"}, "ftpPassword": {"type": "string"}}, "type": "object"}, "example": {"ftpUrl": "ftps://upload.example.com", "ftpUser": "user123", "ftpPassword": "eyJ0eXA"}}}}, "400": {"$ref": "#/components/responses/400"}, "401": {"$ref": "#/components/responses/401"}, "429": {"$ref": "#/components/responses/429"}, "500": {"$ref": "#/components/responses/500"}}}}}, "components": {"schemas": {"Pagination": {"properties": {"page": {"type": "integer", "format": "int32"}, "perPage": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "maxPage": {"type": "integer", "format": "int32"}, "offset": {"type": "integer"}}, "type": "object"}}, "responses": {"400": {"description": "Bad request", "content": {"application/json": {"schema": {}, "example": {"message": "Bad request"}}}}, "401": {"description": "Invalid credentials", "content": {"application/json": {"schema": {}, "example": {"message": "Invalid credentials"}}}}, "402": {"description": "Insufficient balance", "content": {"application/json": {"schema": {}, "example": {"message": "Insufficient balance"}}}}, "403": {"description": "Not allowed", "content": {"application/json": {"schema": {}, "example": {"message": "Not allowed"}}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {}, "example": {"message": "Not found"}}}}, "409": {"description": "Conflict occurred", "content": {"application/json": {"schema": {}, "example": {"message": "Conflict occurred"}}}}, "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {}, "example": {"message": "Rate limit exceeded"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {}, "example": {"message": "Internal server error"}}}}}, "parameters": {"id": {"name": "id", "in": "path", "description": "ID", "required": true, "schema": {"type": "string"}}, "page": {"name": "page", "in": "query", "description": "Page number", "required": false, "schema": {"type": "integer", "format": "int32"}}, "perPage": {"name": "perPage", "in": "query", "description": "Items per page", "required": false, "schema": {"type": "integer", "format": "int32"}}, "status": {"name": "status", "in": "query", "description": "Status", "required": false, "schema": {"type": "string"}}, "type": {"name": "type", "in": "query", "description": "Type", "required": false, "schema": {"type": "string"}}, "search": {"name": "search", "in": "query", "description": "Search", "required": false, "schema": {"type": "string"}}}, "securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Your active API Token", "name": "api-token", "in": "header"}}}, "security": [{"ApiKeyAuth": []}], "tags": [{"name": "Platform", "description": "get platform informations"}, {"name": "User", "description": "User related operations"}, {"name": "Team", "description": "Team related operations"}, {"name": "Billing", "description": "Billing related operations"}, {"name": "Support", "description": "Support related operations"}, {"name": "Referral", "description": "Referral related operations"}, {"name": "Video", "description": "Video management api"}]}