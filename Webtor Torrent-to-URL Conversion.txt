---

## Webtor Torrent-to-URL Conversion

### Overview

The Webtor integration provides torrent-to-direct-URL conversion functionality, allowing magnet links and torrent files to be converted into HTTP URLs that can be uploaded to video hosting platforms.

### Configuration

```python
# Webtor API configuration
WEBTOR_API_URL = os.environ.get('WEBTOR_API_URL', 'https://api.webtor.io')

class WebTorService:
    def __init__(self):
        self.api_url = WEBTOR_API_URL.rstrip('/')
        self.timeout = aiohttp.ClientTimeout(total=60)
```

### Magnet Link to URLs Conversion Logic

```python
async def convert_magnet_to_urls(self, magnet_uri: str) -> dict:
    """Convert magnet link to direct download URLs using Webtor API"""
```

#### Step 1: Magnet Validation
```python
if not magnet_uri.startswith('magnet:'):
    raise ValueError("Invalid magnet URI format")
```

#### Step 2: Store Magnet in Webtor
```python
async with aiohttp.ClientSession(timeout=self.timeout) as session:
    # Store magnet in Webtor (free tier - no API key needed)
    store_payload = {
        "magnet": magnet_uri,
        "expire": 3600  # 1 hour expiration
    }
    
    async with session.post(f"{self.api_url}/torrent", json=store_payload) as response:
        if response.status == 200:
            result = await response.json()
            info_hash = result.get("infoHash")
```

#### Step 3: Retrieve Torrent Information
```python
# Wait for processing
await asyncio.sleep(3)

# Get torrent information and files
async with session.get(f"{self.api_url}/torrent/{info_hash}") as info_response:
    if info_response.status == 200:
        torrent_data = await info_response.json()
        
        files = []
        torrent_files = torrent_data.get("files", [])
        
        for file_info in torrent_files:
            file_path = file_info.get("path", "")
            file_name = file_info.get("name", file_path.split("/")[-1])
            file_size = file_info.get("length", 0)
            
            # Generate direct download URL
            download_url = f"{self.api_url}/get/{info_hash}/{file_path}"
            
            files.append({
                "name": file_name,
                "path": file_path,
                "size": file_size,
                "download_url": download_url
            })
```

#### Step 4: Return Conversion Results
```python
return {
    "success": True,
    "info_hash": info_hash,
    "name": torrent_data.get("name", "Unknown"),
    "files": files,
    "total_files": len(files)
}
```

### API Endpoints for Torrent Conversion

#### 1. Convert Magnet to URLs
**Endpoint**: `POST /api/convert/magnet`

**Request Body**:
```json
{
    "magnet_uri": "magnet:?xt=urn:btih:..."
}
```

**Response**:
```json
{
    "success": true,
    "info_hash": "abc123...",
    "name": "Video Collection",
    "files": [
        {
            "name": "video1.mp4",
            "path": "folder/video1.mp4",
            "size": 1048576,
            "download_url": "https://api.webtor.io/get/abc123.../folder/video1.mp4"
        }
    ],
    "total_files": 1
}
```

#### 2. Submit Converted URLs for Upload
**Endpoint**: `POST /api/upload/submit_converted_urls`

**Request Body**:
```json
{
    "urls": ["http://url1", "http://url2"],
    "source_type": "torrent_conversion",
    "source_info": {
        "info_hash": "abc123...",
        "name": "Video Collection"
    }
}
```

### Integration with Upload System

```python
# Create upload tasks for each converted URL
created_tasks = []
for url in urls:
    filename = url.split('/')[-1] or f"torrent_file_{len(created_tasks)+1}"
    if source_info.get("name"):
        filename = f"{source_info['name']}_{len(created_tasks)+1}"
    
    task = UploadTask(
        url=url,
        upload_type=UploadType.REMOTE_URL,
        filename=filename,
        session_id=current_session_id or str(uuid.uuid4()),
        # Add source tracking for converted torrents
        infohash=source_info.get("info_hash"),
        parent_url=source_info.get("magnet_uri", "torrent_conversion")
    )
    
    upload_queue.append(task)
    created_tasks.append(task)
```