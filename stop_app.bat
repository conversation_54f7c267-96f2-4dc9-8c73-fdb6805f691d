@echo off
echo ========================================
echo   Video Upload Automation Hub Stopper
echo ========================================
echo.

echo Stopping all related processes...
echo.

:: Kill processes by port numbers
echo Checking for Backend processes on port 8001...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001') do (
    echo Killing backend process PID: %%a
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Backend process stopped
    )
)

echo.
echo Checking for Frontend processes on port 3000...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    echo Killing frontend process PID: %%a
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Frontend process stopped
    )
)

echo.
echo Killing any remaining Python/Node processes...

:: Kill Python processes that might be running uvicorn or server.py
echo Killing Python processes (uvicorn/server.py)...

:: Method 1: Kill by port 8001 (most reliable)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001 ^| findstr LISTENING') do (
    echo Killing process on port 8001, PID: %%a
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Backend process on port 8001 stopped
    )
)

:: Method 2: Kill Python processes with uvicorn/server.py in command line
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul') do (
    for /f %%b in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| findstr /i "uvicorn"') do (
        echo Killing Python uvicorn process PID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if not errorlevel 1 echo ✓ Python uvicorn process stopped
    )
    for /f %%c in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| findstr /i "server.py"') do (
        echo Killing Python server.py process PID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if not errorlevel 1 echo ✓ Python server.py process stopped
    )
)

:: Method 3: Fallback - kill all Python processes (aggressive)
echo Checking for any remaining Python processes...
tasklist /fi "imagename eq python.exe" /fo table /nh 2>nul | findstr python.exe >nul && (
    echo WARNING: Killing ALL remaining Python processes...
    taskkill /f /im python.exe >nul 2>&1
    if not errorlevel 1 echo ✓ All Python processes terminated
)

:: Kill Node processes that might be running React dev server
echo Killing Node.js processes (react-scripts)...

:: Method 1: Kill by port 3000 (most reliable for frontend)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
    echo Killing Node.js process on port 3000, PID: %%a
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo ✓ Node.js process on port 3000 stopped
    )
)

:: Method 2: Kill Node processes with react-scripts in command line
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq node.exe" /fo table /nh 2^>nul') do (
    for /f %%b in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| findstr /i "react-scripts"') do (
        echo Killing Node React process PID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if not errorlevel 1 (
            echo ✓ Node.js React process stopped
        )
    )
    for /f %%c in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| findstr /i "npm.*start"') do (
        echo Killing Node npm start process PID: %%a
        taskkill /f /pid %%a >nul 2>&1
        if not errorlevel 1 (
            echo ✓ Node.js npm start process stopped
        )
    )
)

:: Method 3: Fallback - kill all Node processes (aggressive)
echo Checking for any remaining Node.js processes...
tasklist /fi "imagename eq node.exe" /fo table /nh 2>nul | findstr node.exe >nul && (
    echo WARNING: Killing ALL remaining Node.js processes...
    taskkill /f /im node.exe >nul 2>&1
    if not errorlevel 1 echo ✓ All Node.js processes terminated
)

:: Close any command windows with our server titles
echo.
echo Closing server terminal windows...
taskkill /f /fi "WindowTitle eq Backend Server*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Frontend Server*" >nul 2>&1

echo.
echo ========================================
echo   All processes have been stopped!
echo ========================================
echo.
echo You can now safely:
echo - Run start_app.bat to restart
echo - Make code changes
echo - Close this window
echo.
pause
