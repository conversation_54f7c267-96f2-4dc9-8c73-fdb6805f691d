# Video Upload Automation Hub - Environment Configuration
# Copy this file to .env and fill in your actual API keys

# =============================================================================
# VIDEO HOSTING PLATFORM API KEYS
# =============================================================================
# Get your API keys from each platform's developer/API section

# Lulustream API Key
# Get from: https://lulustream.com/api
LULUSTREAM_API_KEY=your_lulustream_api_key_here

# StreamP2P API Key  
# Get from: https://streamp2p.com/api
STREAMP2P_API_KEY=your_streamp2p_api_key_here

# RPMShare API Key
# Get from: https://rpmshare.com/api  
RPMSHARE_API_KEY=your_rpmshare_api_key_here

# FileMoon API Key
# Get from: https://filemoon.to/api
FILEMOON_API_KEY=your_filemoon_api_key_here

# UpnShare API Key
# Get from: https://upnshare.com/api
UPNSHARE_API_KEY=your_upnshare_api_key_here

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Backend Server Configuration
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8001

# Frontend Configuration
REACT_APP_BACKEND_URL=http://localhost:8001

# =============================================================================
# UPLOAD CONFIGURATION
# =============================================================================

# Maximum number of files that can be uploaded concurrently (5 recommended)
MAX_CONCURRENT_UPLOADS=5

# Maximum number of files that can be queued at once (50 max as per requirements)
MAX_QUEUE_SIZE=50

# Upload timeout in minutes (30 minutes recommended)
UPLOAD_TIMEOUT_MINUTES=30

# Progress update interval in seconds
PROGRESS_UPDATE_INTERVAL=5

# =============================================================================
# AUDIO NOTIFICATIONS
# =============================================================================

# Enable/disable audio notifications
REACT_APP_AUDIO_ENABLED=true

# Audio file paths (relative to public folder)
REACT_APP_COMPLETION_AUDIO=/audio/completion.mp3
REACT_APP_ERROR_AUDIO=/audio/error.mp3

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Directory for temporary file storage
TEMP_DIR=./temp

# Directory for data persistence
DATA_DIR=./data

# CSV output directory
CSV_OUTPUT_DIR=./csv_output

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Enable detailed API logging
API_DEBUG_LOGGING=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Session secret key (generate a random string)
SESSION_SECRET=your_random_session_secret_here

# =============================================================================
# PLATFORM-SPECIFIC CONFIGURATION
# =============================================================================

# Lulustream specific settings
LULUSTREAM_MAX_RETRIES=3
LULUSTREAM_TIMEOUT=300

# StreamP2P specific settings  
STREAMP2P_MAX_RETRIES=3
STREAMP2P_TIMEOUT=300

# RPMShare specific settings
RPMSHARE_MAX_RETRIES=3
RPMSHARE_TIMEOUT=300

# FileMoon specific settings
FILEMOON_MAX_RETRIES=3
FILEMOON_TIMEOUT=600

# UpnShare specific settings
UPNSHARE_MAX_RETRIES=3
UPNSHARE_TIMEOUT=300

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development mode (true/false)
DEVELOPMENT_MODE=true

# Enable hot reload for frontend
REACT_APP_HOT_RELOAD=true

# Disable host check for development
DANGEROUSLY_DISABLE_HOST_CHECK=true
