/* Modern Video Upload Hub Styles */

.App {
  text-align: left;
}

/* Remove duplicate scrollbar styles - handled in index.css */

/* Platform-specific colors and gradients */
.lulustream {
  border-color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

.streamp2p {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.rpmshare {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.filemoon {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.upnshare {
  border-color: #ec4899;
  background: rgba(236, 72, 153, 0.1);
}

/* Enhanced progress bar colors - reduced by 25% */
.progress-lulustream {
  background: #8b5cf6;
  box-shadow: 0 0 7.5px rgba(139, 92, 246, 0.5); /* 10px -> 7.5px */
}

.progress-streamp2p {
  background: #3b82f6;
  box-shadow: 0 0 7.5px rgba(59, 130, 246, 0.5); /* 10px -> 7.5px */
}

.progress-rpmshare {
  background: #10b981;
  box-shadow: 0 0 7.5px rgba(16, 185, 129, 0.5); /* 10px -> 7.5px */
}

.progress-filemoon {
  background: #f59e0b;
  box-shadow: 0 0 7.5px rgba(245, 158, 11, 0.5); /* 10px -> 7.5px */
}

.progress-upnshare {
  background: #ec4899;
  box-shadow: 0 0 7.5px rgba(236, 72, 153, 0.5); /* 10px -> 7.5px */
}

/* Status colors with better contrast - reduced by 25% */
.status-queued {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
  padding: 1.5px 6px; /* 2px 8px -> 1.5px 6px */
  border-radius: 3px; /* 4px -> 3px */
  font-weight: 500;
}

.status-uploading {
  color: #60a5fa;
  background: rgba(96, 165, 250, 0.1);
  padding: 1.5px 6px; /* 2px 8px -> 1.5px 6px */
  border-radius: 3px; /* 4px -> 3px */
  font-weight: 500;
}

.status-processing {
  color: #a855f7;
  background: rgba(168, 85, 247, 0.1);
  padding: 1.5px 6px; /* 2px 8px -> 1.5px 6px */
  border-radius: 3px; /* 4px -> 3px */
  font-weight: 500;
}

.status-completed {
  color: #34d399;
  background: rgba(52, 211, 153, 0.1);
  padding: 1.5px 6px; /* 2px 8px -> 1.5px 6px */
  border-radius: 3px; /* 4px -> 3px */
  font-weight: 500;
}

.status-failed, .status-error {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
  padding: 1.5px 6px; /* 2px 8px -> 1.5px 6px */
  border-radius: 3px; /* 4px -> 3px */
  font-weight: 500;
}

/* Enhanced animations - remove duplicates, use Tailwind animations - reduced by 25% */
.upload-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-card:hover {
  transform: translateY(-3px); /* -4px -> -3px */
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4); /* 20px 40px -> 15px 30px */
}

/* Button hover effects */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Focus styles for accessibility */
button:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}