Also below is the API Endpoint for filemoon…. See if filemoon is implemented correctly

[![](https://filemoon.sx/assets/images/logo.svg)](https://filemoon.sx/)

[Premium](https://filemoon.sx/premium) [Make Money](https://filemoon.sx/affiliate)

[Log In](https://filemoon.sx/login) [Sign Up](https://filemoon.sx/register)

[![](https://filemoon.to/assets/images/logo.svg)](https://filemoonapi.com/)

API Documentation

- [Introduction](https://filemoon.sx/api#intro)
- [Account](https://filemoon.sx/api#account)[Account Info](https://filemoon.sx/api#account-1) [Account Stats](https://filemoon.sx/api#account-2)
- [Upload](https://filemoon.sx/api#upload)[Get Upload Server](https://filemoon.sx/api#upload-1) [Upload To Server](https://filemoon.sx/api#upload-2)
- [Remote Upload](https://filemoon.sx/api#remote)[Add Remote Upload](https://filemoon.sx/api#remote-1) [Remove Remote Upload](https://filemoon.sx/api#remote-2) [Check Status](https://filemoon.sx/api#remote-3)
- [File](https://filemoon.sx/api#file)[File Info](https://filemoon.sx/api#file-1) [File List](https://filemoon.sx/api#file-2) [File Clone](https://filemoon.sx/api#file-4) [File Set Folder](https://filemoon.sx/api#file-5)
- [Folders](https://filemoon.sx/api#folder)[Folder List](https://filemoon.sx/api#folder-1) [Create Folder](https://filemoon.sx/api#folder-2)
- [Files](https://filemoon.sx/api#files)[Deleted Files](https://filemoon.sx/api#files-1) [DMCA Files](https://filemoon.sx/api#files-2)
- [Player](https://filemoon.sx/api#player)[Remote Subtitle](https://filemoon.sx/api#player-1) [Remote Subtitles JSON](https://filemoon.sx/api#player-2) [Remote Poster](https://filemoon.sx/api#player-3) [Remote Logo](https://filemoon.sx/api#player-4)
- [Encoding](https://filemoon.sx/api#encoding)[Encoding List](https://filemoon.sx/api#encoding-1) [Encoding Status Per File](https://filemoon.sx/api#encoding-2) [Restart Errors](https://filemoon.sx/api#encoding-3) [Delete Errors](https://filemoon.sx/api#encoding-4)
- [Thumbs](https://filemoon.sx/api#images)[Thumbnail Image](https://filemoon.sx/api#images-1) [Splash Image](https://filemoon.sx/api#images-2) [Video Preview](https://filemoon.sx/api#images-3)
- [Embed Domains](https://filemoon.sx/api#domains)[Get Embed Domain](https://filemoon.sx/api#domains-1)
- [Premium Bandwidth](https://filemoon.sx/api#premium)[Get HLS Link](https://filemoon.sx/api#premium-1)

[Home](https://filemoon.sx/api#)/Api

# Introduction

FileMoon API

# Account

My Account functions

### Account Info

GET

https://filemoonapi.com/api/account/info?key=key

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:30:07",
    "status": 200,
    "result": {
        "email": "<EMAIL>",
        "balance": "0.00000",
        "storage_used" :"********",
        "storage_left": ************,
        "premim_expire": "2015-10-24 21:00:00
    }
}

```

### Account Stats

GET

https://filemoonapi.com/api/account/stats?key=key&last=last

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **last** | show stats for last X days Default:7 | No | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2017-08-11 04:30:07",
  "status": 200,
  "result": [\
    {\
      "downloads": "0",\
      "profit_views": "0.00000",\
      "views_adb": "1",\
      "sales": "0",\
      "profit_sales": "0.00000",\
      "profit_refs": "0.00000",\
      "profit_site": "0.00000",\
      "views": "0",\
      "refs": "0",\
      "day": "2017-09-12",\
      "profit_total": "0.00000",\
      "views_prem": "0"\
    }\
  ]
}

```

# Upload

### Get Upload Server

##### Get next Upload Server URL

Please note you should fetch new upload server everytime you want to upload new file.

GET

https://filemoonapi.com/api/upload/server?key=key

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:29:54",
    "status": 200,
    "result": "https://filemoonapi.com/upload/01"
  }

```

### Upload To Server

##### Post your file with all required params to the server.

POST

https://moon-upload-server-01.filemoon.to/upload/01


#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **file** | file Example:xxxx.mp4 | Yes | File |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
"msg": "OK",
"status": 200,
"files": [ {\
	"filecode":"tnklyibwwpsh",\
	"filename": "qwbs4m4ze4j4.mp4",\
	"status": "OK" } ]
}

```

### Add Remote Upload

GET

https://filemoonapi.com/api/remote/add?key=key&url=url

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **url** | URL to video file Example:http://site.com/v.mkv | No | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
"msg": "OK",
"server_time": "2017-08-11 04:29:54",
"status": 200,
	"result": {
		"filecode": "jthi5jdsu8t9"
    }
}

```

### Remove Remote Upload

GET

https://filemoonapi.com/api/remote/remove?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **file\_code** | File Code Example:af12cxzbr23143 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
"msg": "File succesfully removed from remote upload queue",
"server_time": "2017-08-11 04:29:54",
"status": 200,
}

```

### Check Remote Status

GET

https://filemoonapi.com/api/remote/status?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **file\_code** | File Code Example:af12cxzbr23143 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
"msg": "OK",
"server_time": "2017-08-11 04:29:54",
"status": 200,
"result": {
		"url": "https://exampleurl.com/file.mp4",
		"progress": "55%",
		"status": "WORKING",
		"created": "10:00:00 09-06-2022",
		"updated": "10:01:00 09-06-2022",
		"error_msg": "",
		}
}

```

# File

### File Info

Get info/check file(s)

GET

https://filemoonapi.com/api/file/info?key=key&file\_code=file\_code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **file\_code** | file code, or list separated by comma <br>Example:gi4o0tlro01u,gi4o0tlro012 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2017-08-11 04:28:53",
  "status": 200,
  "result": [\
    {\
      "status": 200,\
      "file_code": "gi4o0tlro01u",\
      "name": "4K Time Lapse in the EOS 6D Mark II",\
      "canplay": 1,\
      "views_started": "1",\
      "views": "0",\
      "length": "20",\
      "uploaded": "2017-08-10 05:07:17"\
    },\
    {\
      "status": 404,\
      "file_code": "gi4o0tlro012"\
    }\
  ]
}

```

### File List

GET

https://filemoonapi.com/api/file/list?key=1l5ftrilhllgwx2bo&page=2&per\_page=20&fld\_id=15&public=1&created=2018-06-21%2005%3A07%3A10&name=Iron%20man

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **page** | page number Example:2 | No | Number |
| **per\_page** | number of results per page Example:20 | No | Number |
| **fld\_id** | folder id Example:15 | No | Number |
| **public** | show public (1) or private (0) files only Example:1 | No | Number |
| **created** | show only files uploaded after timestamp. Specify number to show only files uploaded X minutes ago. <br>Example:2018-06-21 05:07:10 | No | String |
| **title** | filter video titles Example:Iron man | No | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:28:53",
    "status": 200,
    "result": [\
      {\
        "status": 200,\
        "filecode": "gi4o0tlro01u",\
        "name": "4K Time Lapse in the EOS 6D Mark II",\
        "canplay": 1,\
        "views_started": "1",\
        "views": "0",\
        "length": "20",\
        "uploaded": "2017-08-10 05:07:17"\
      },\
      {\
        "status": 404,\
        "filecode": "gi4o0tlro012"\
      }\
    ]
  }

```

### File Clone

GET

https://filemoonapi.com/api/file/clone?key=key&file\_code=file\_code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **file\_code** | file code<br> Example:gi4o0tlro01u,gi4o0tlro012 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:28:53",
    "status": 200,
    "result": {
		"file_code": "gtl2mhgw4is7",
		"url": "https://filemoonapi.com/d/gtl2mhgw4is7"
    }
}

```

### File Set Folder

GET

https://filemoonapi.com/api/file/clone?key=key&file\_code=file\_code&fld\_id=fld\_id

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **file\_code** | file code<br> Example:gi4o0tlro01u,gi4o0tlro012 | Yes | String |
| **fld\_id** | folder id<br> Example:15 | Yes | Number |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:28:53",
    "status": 200
}

```

# Folder

### Folder List

Get folder/file list

GET

https://filemoonapi.com/api/folder/list?key=key&fld\_id=fld\_id

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **fld\_id** | Folder ID Example:15 | No | Number |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2017-08-11 04:28:53",
  "status": 200,
  "result": {
    "folders": [\
      {\
        "name": "Sub1",\
        "fld_id": "24"\
      }\
    ],
    "files": [\
      {\
        "link": "https://filemoonapi.com/4w0sy8e63f0c.html",\
        "uploaded": "2017-07-18 04:06:39",\
        "file_code": "4w0sy8e63f0c",\
        "fld_id": "16",\
        "title": "000 video sample2 s",\
        "canplay": 0\
      }\
    ]
  }
}

```

### Create Folder

GET

https://filemoonapi.com/api/folder/create?key=key&parent\_id=parent\_id&name=name

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **parent\_id** | parent folder id Example:15 | No | Number |
| **name** | folder name Example:New Folder | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2017-08-11 04:28:53",
  "status": 200,
  "result": {
    "fld_id": "26"
  }
}

```

# Files

### Deleted Files

Get last files deleted

GET

https://filemoonapi.com/api/files/deleted?key=key&last=last

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **last** | number of files limit Example:20 | No | Number |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg":"OK",
    "server_time":"2017-08-11 04:28:53",
    "status":200,
    "result":
    [\
        {\
            "file_code":"gtl2mhgw4is7",\
            "title":"New title",\
            "deleted":"2017-08-11 20:00:01",\
            "deleted_ago_sec":"194316"\
        },\
        {\
            "file_code":"uoyagho8c707",\
            "title":"000-video-sample2s",\
            "deleted":"2017-08-10 20:30:02"\
            "deleted_ago_sec":"278915"\
        }\
    ]
}

```

### DMCA Files

This works with DMCA mod enabled

GET

https://filemoonapi.com/api/files/dmca?key=key&last=last

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **last** | number of files limit Example:20 | No | Number |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2017-08-11 04:28:53",
  "status": 200,
  "result": [\
    {\
      "file_code": "gtl2mhgw4is7",\
      "del_time": "2017-08-11 16:31:53"\
    },\
    {\
      "file_code": "uoyagho8c707",\
      "del_time": "2017-08-11 16:30:23"\
    }\
  ]
}

```

# Player

### Remote Subtitle

Add remote subtitle via embed/direct video link

GET

https://filemoonapi.com/e/file\_code?cX\_file=https://example.com/file.vtt&cX\_label=English


#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **cX\_file** | c1\_file (replace x with number) Example:c1\_file=https://example.com | Yes | String |
| **cX\_label** | c1\_label (replace x with number)Example:c1\_label=English | Yes | String |

### Remote Subtitles JSON

Add multiple subtitles via remote json file.

GET

https://filemoonapi.com/e/file\_code?sub.info=http://yoursubtitle.com/def.json


### JSON File Sample

The default attribute is optional and is "false" if you do not set it. You must also set appropiate CORS Headers (e.g. Access-Control-Allow-Origin) as the viewers browser will fetch the JSON file. You also have to use VTT format.

```javascript

[\
 {"src":"http://yoursubtitle.com/file.vtt", "label":"Language1", default: true},\
 {"src":"http://yoursubtitle.com/file2.vtt", "label":"Language2"}\
]

```

### Remote Poster

Add your own poster via embed/direct link, image has to be .png or .jpg.

GET

https://filemoonapi.com/e/file\_code?poster=http://yoursite.com/link/to.png


### Remote Logo

Add your own logo into player via embed/direct link, image has to be .png or .jpg.

GET

https://filemoonapi.com/e/file\_code?logo=http://yoursite.com/link/to.png


### Encoding List

GET

https://filemoonapi.com/api/encoding/list?key=key

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2021-12-08 10:53:45",
  "status": 200,
  "result": [\
    {\
      "quality": "X",\
	  "name": "This file title",\
      "progress": 0,\
      "status": "ERROR",\
      "error": "File download failed:500",\
	  "file_code": "xx1234cccs",\
    },\
    {\
      "file_code": "xx1234cccs",\
      "quality": "H",\
	  "name": "This file title2",\
      "progress": "91",\
      "status": "ENCODING",\
    }\
  ]
}

```

### Encoding Status Per File

GET

https://filemoonapi.com/api/encoding/status?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **code** | File Code Example:asdcxz124bvbg3 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "OK",
  "server_time": "2021-12-08 10:53:45",
  "status": 200,
  "result":
    {
      "file_code": "xx1234cccs",
      "quality": "H",
	  "name": "This file title2",
      "progress": "91",
      "status": "ENCODING",
    }
}

```

### Restart Encoding Errors

GET

https://filemoonapi.com/api/encoding/restart?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **code** | File Code Example:asdcxz124bvbg3 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "Encoding for file code: xxx12313mcmc was restarted",
  "server_time": "2021-12-08 10:53:45",
  "status": 200,
}

```

### Delete Errors

GET

https://filemoonapi.com/api/encoding/delete?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **code** | File Code Example:asdcxz124bvbg3 | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
  "msg": "Failed encoding for file code: xxx21347823n was deleted.",
  "server_time": "2021-12-08 10:53:45",
  "status": 200,
}

```

### Thumbnail Image Url

GET

https://filemoonapi.com/api/images/thumb?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **code** | File Code Example:as1xc34vv12x | No | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:29:54",
    "status": 200,
    "result": {
        "thumbnail": "https://img-place.com/as1xc34vv12x.png"
    }
}

```

### Splash Image Url

GET

https://filemoonapi.com/api/images/splash?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **code** | File Code Example:as1xc34vv12x | No | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
    "msg": "OK",
    "server_time": "2017-08-11 04:29:54",
    "status": 200,
    "result": {
        "thumbnail": "https://img-place.com/as1xc34vv12x_xt.png"
    }
}

```

### Video Preview Url

GET

https://filemoonapi.com/api/images/preview?key=key&file\_code=code

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **code** | File Code Example:as1xc34vv12x | No | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
        "msg": "OK",
        "server_time": "2017-08-11 04:29:54",
        "status": 200,
		"result": {
			"thumbnail": "https://img-place.com/as1xc34vv12x.mp4"
		}
}

```

### Get Embed Domain

GET

https://filemoonapi.com/api/get/domain?key=key

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
"old_domain":"filemoon.sx",
"new_domain":"blablabla_embed_domain.com",
"status":200,
"server_time":"2024-12-03 08:32:03"
}

```

### Get HLS Link

GET

https://filemoonapi.com/api/hls/link?key=key&ip=ip address&ua=user agent

#### Parameters

| Name | Description | Required | Type |
| --- | --- | --- | --- |
| **key** | API key Example:1l5ftrilhllgwx2bo | Yes | String |
| **key** | IP Address Example:************** (Must Be IPv4) | Yes | String |
| **key** | User Agent Example:Mozilla/5.0 (iPhone; CPU iPhone OS 13\_5\_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1 (Must be URLENCODED!) | Yes | UrlEncoded String |

### Response

**200**

###### Headers

Content-Type: application/json

```javascript

{
"server_time":"2025-02-20 06:57:03",
"result":"https://premium.filemoonapi.com/hls2/01/08264/2231iyrBNMfj_h/master.m3u8?t=okJkln83voSDg20Gm7Mb9givk-7ci9Z7MDazB5GE&s=**********&e=7200&f=********&srv=122&asn=12192&sp=4000&p=1",
"status":200,
"msg":"OK"
}

```

FileMoon © 2022

[Home](https://filemoon.sx/) [Terms of Service](https://filemoon.sx/tos) [Privacy Policy](https://filemoon.sx/privacy) [Copyright Policy](https://filemoon.sx/copyright) [Make Money](https://filemoon.sx/affiliate) [API Documentation](https://filemoon.sx/api) [Server Status](https://status.filemoon.sx/)

[Home](https://filemoon.sx/) [My account](https://filemoon.sx/dashboard) [Premium](https://filemoon.sx/premium) [Make Money](https://filemoon.sx/affiliate)

* * *

[Terms of Service](https://filemoon.sx/tos) [Privacy Policy](https://filemoon.sx/privacy) [API Documentation](https://filemoon.sx/api) [Server Status](https://status.filemoon.sx/)
