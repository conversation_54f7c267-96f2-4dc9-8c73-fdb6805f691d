@tailwind base;
@tailwind components;
@tailwind utilities;

/* Deep Black Dark Mode Theme */
@layer base {
  * {
    @apply border-gray-800;
  }

  body {
    background: #141414;
    color: #e5e7eb; /* gray-200 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  html {
    background: #141414;
  }
}

/* Global 25% size reduction utilities */
@layer utilities {
  .size-reduced {
    --scale-factor: 0.75;
  }
}

/* Custom scrollbar - reduced by 25% */
::-webkit-scrollbar {
  width: 6px;  /* 8px -> 6px */
  height: 6px; /* 8px -> 6px */
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-700 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* Custom animations - reduced by 25% */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 3.75px rgba(59, 130, 246, 0.5); /* 5px -> 3.75px */
  }
  50% {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);    /* 20px -> 15px */
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes progress-indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-indeterminate::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
  animation: progress-indeterminate 1.5s ease-in-out infinite;
}

/* Platform specific colors - simplified, detailed styles in App.css */
.lulustream { @apply text-purple-400 border-purple-500; }
.streamp2p { @apply text-blue-400 border-blue-500; }
.rpmshare { @apply text-green-400 border-green-500; }
.filemoon { @apply text-yellow-400 border-yellow-500; }
.upnshare { @apply text-pink-400 border-pink-500; }

/* Status colors - basic fallbacks, enhanced styles in App.css */
.status-queued { @apply text-gray-400; }
.status-uploading { @apply text-blue-400; }
.status-processing { @apply text-yellow-400; }
.status-completed { @apply text-green-400; }
.status-failed { @apply text-red-400; }
.status-cancelled { @apply text-gray-500; }