Also below is the API Endpoint for Lulustream…. See if Lulustream is implemented correctly

Account InfoAccount StatsGet Upload ServerUpload FileUpload by URLFile InfoFile EditFile ListFile CloneDeleted FilesDMCA FilesEncodingsURL UploadsURL Uploads ActionsFolder ListFolder CreateFolder Edit

## Account Info

**REQUEST**

```
GET https://lulustream.com/api/account/info?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |

**RESPONSE**

```
{
		    "msg": "OK",
		    "server_time": "2021-09-11 04:30:07",
		    "status": 200,
		    "result": {
		    	"files_total":"31",
		    	"storage_left":**********,
		    	"login":"megauploader21",
		        "email": "<EMAIL>",
		        "premium_expire":"2022-10-15 04:46:59",
		        "balance":"108.00000",
		        "premium":1,
		        "storage_used":"685101"
		    }
		}
```

## Account Stats

**REQUEST**

```
GET https://lulustream.com/api/account/stats?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| last | show stats for last X days (default: 7) | 14 | INT |  |

**RESPONSE**

```
{
		  "msg": "OK",
		  "server_time": "2021-09-13 04:30:07",
		  "status": 200,
		  "result": [\
		    {\
		      "downloads": "0",\
		      "profit_views": "0.00000",\
		      "views_adb": "1",\
		      "sales": "0",\
		      "profit_sales": "0.00000",\
		      "profit_refs": "0.00000",\
		      "profit_site": "0.00000",\
		      "views": "0",\
		      "refs": "0",\
		      "day": "2021-09-12",\
		      "profit_total": "0.00000",\
		      "views_prem": "0"\
		    }\
		  ]
		}
```

## Get Upload Server

**REQUEST**

```
GET https://lulustream.com/api/upload/server?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |

**RESPONSE**

```
{
		  "msg": "OK",
		  "server_time": "2021-08-11 04:29:54",
		  "status": 200,
		  "result": "https://s1.myvideo.com/upload/01"
		}
```

## Upload File to Server

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| file | Video file(s) | 1.avi | FILE |  |
| file\_title | Title of video | New release video | STRING |  |
| file\_descr | Description of video | This is sample from<br>our upcoming release | STRING |  |
| snapshot | Custom video snapshot (up to 300KB) | 1.jpg | FILE |  |
| fld\_id | Folder ID | 25 | INT |  |
| cat\_id | Category ID | 5 | INT |  |
| tags | Tags list | promo, high quality | STRING |  |
| file\_public | Public flag | 1 | INT |  |
| file\_adult | Adult flag | 1 | INT |  |
| html\_redirect | Use old style HTML redirect output instead of JSON | 1 | INT |  |

**Minimal HTML form upload sample:**

```
<form method="POST" enctype="multipart/form-data" action="https://s1.myvideo.com/upload/01">
		<input type="hidden" name="key" value="45pqv0iqypy3ephqk">
		<input type="hidden" name="html_redirect" value="1">
		<input type="file" name="file">
		<input type="submit">
		</form>
```

**CURL file upload sample:**
Upload 2 videos:


```
curl -X POST -F 'key=45pqv0iqypy3ephqk' -F 'file=@1.avi' -F 'file=@2.avi' https://s1.myvideo.com/upload/01
```

Upload video with custom title and snapshot:


```
curl -X POST -F 'key=1l5ftrilhllgwx2bo' -F 'file_title="Hola!"' -F 'file=@1.avi' -F 'snapshot=@1.jpg' http://s1.lulustream.com/upload/01
```

Response:


```
{
		    "msg": "OK",
		    "status": 200,
		    "files": [{\
		        "filecode": "u9150wqzvhxj",\
		        "filename": "1.avi",\
		        "status": "OK"\
		    }, {\
		        "filecode": "gzky98gfg6hn",\
		        "filename": "2.avi",\
		        "status": "OK"\
		    }]
		}
```

## Upload by URL

**REQUEST**

```
GET https://lulustream.com/api/upload/url?key=45pqv0iqypy3ephqk&url={url}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| url | URL to video file | http://clips.vorwaerts-gmbh.de/big\_buck\_bunny.mp4 | STRING |  |
| fld\_id | Folder ID | 25 | INT |  |
| cat\_id | Category ID | 5 | INT |  |
| file\_public | Public flag | 1 | INT |  |
| file\_adult | Adult flag | 1 | INT |  |
| tags | Tags list | promo, high quality | STRING |  |

**RESPONSE**
returns future filecode that will work after upload complete


```
{
			"msg":"OK",
			"server_time":"2021-08-12 20:56:47",
			"status":200,
			"result":{
				"filecode":"fb5asfuj2snh"
			}
		}
```

## Upload Subtitle

**REQUEST**

```
GET https://lulustream.com/api/upload/sub?key=45pqv0iqypy3ephqk&file_code={file_code}&sub_lang={sub_lang}&sub_url={url}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING | yes |
| file\_code | file code | gi4o0tlro01u | STRING | yes |
| sub\_lang | Subtitle language code | eng | STRING | yes |
| sub\_url | URL to subtitle file | https://lulustream.com/1.srt | STRING |  |
| sub\_file | Subtitle file attachment | 1.vtt | FILE |  |

**CURL subtitle file upload sample:**

```
curl -X POST -F 'key=45pqv0iqypy3ephqk' -F 'file_code=cnypqbf2t79l' -F 'sub_lang=eng' -F 'sub_file=@1.vtt' https://lulustream.com/api/upload/sub
```

**RESPONSE**
returns future filecode that will work after upload complete


```
{
		"msg":"OK",
		"server_time":"2021-08-12 20:56:47",
		"status":200
	}
```

## File Info

**REQUEST**

```
GET https://lulustream.com/api/file/info?key=45pqv0iqypy3ephqk&file_code={file_code}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| file\_code | file code, or list separated by comma | gi4o0tlro01u,gi4o0tlro012 | STRING |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-12 21:10:07",
			"status":200,
			"result":[\
				{\
				"file_views_full":"0",\
				"cat_id":"3",\
				"player_img":"http://img.lulustream.com/fb5asfuj2snh.jpg",\
				"status":200,\
				"file_code":"fb5asfuj2snh",\
				"file_last_download":"2021-08-12 20:56:54",\
				"canplay":1,\
				"file_public":"1",\
				"file_length":"60",\
				"file_title":"big buck bunny",\
				"file_views":"0",\
				"file_created":"2021-08-102 20:51:52",\
				"file_premium_only":"0",\
				"file_adult":"1",\
				"file_fld_id":"25",\
				"tags":"promo, high quality"\
				}\
			]
		}
```

## File Edit

**REQUEST**

```
GET https://lulustream.com/api/file/edit?key=45pqv0iqypy3ephqk&file_code={file_code}&file_title={file_title}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| file\_code | File code or comma separated list | gi4o0tlro01u,hqpibyqwxumrt | STRING |  |
| file\_title | File title | The 100 S01E02 | STRING |  |
| file\_descr | File description | release by MegaDrive | STRING |  |
| cat\_id | Category ID | 5 | INT |  |
| file\_fld\_id | Folder ID | 25 | INT |  |
| file\_public | Public flag | 1 | INT |  |
| file\_adult | Adult flag | 0 | INT |  |
| tags | File tags | promo, high quality | STRING |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-13 20:17:12",
			"status":200,
			"result":"true"
		}
```

## File List

**REQUEST**

```
GET https://lulustream.com/api/file/list?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| fld\_id | Folder ID | 25 | INT |  |
| title | Filter videos by title | Iron man | STRING |  |
| created | Show only videos uploaded after timestamp.<br>Specify number to show videos uploaded X minutes ago | 2021-07-21 05:07:10 | STRING |  |
| public | Show public (1) or private (0) videos only, empty to show all | 1 | INT |  |
| adult | Show adult (1) or safe (0) videos only, empty to show all | 0 | INT |  |
| per\_page | Number of results per page | 50 | INT |  |
| page | Page number | 2 | INT |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-13 20:35:18",
			"status":200,
			"result":
			{
				"files":[\
					{\
						"thumbnail":"http://img.lulustream.com/fb5asfuj2snh_t.jpg",\
						"link":"http://lulustream.com/fb5asfuj2snh.html",\
						"file_code":"fb5asfuj2snh",\
						"canplay":1,\
						"length":"60",\
						"views":"0",\
						"uploaded":"2021-07-12 20:56:54",\
						"public":"0",\
						"fld_id":"0",\
						"title":"Test 123"\
					}\
				],
				"results_total":9,
				"pages":9,
				"results":1
			}
		}
```

## File Clone

**REQUEST**

```
GET https://lulustream.com/api/file/clone?key=45pqv0iqypy3ephqk&file_code={file_code}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| file\_code | Source file code | abnormamorph | STRING |  |
| file\_title | New file title if required | New video title | STRING |  |
| fld\_id | Target folder id | 5 | INT |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-14 19:39:58",
			"status":200,
			"result":
			{
				"url":"http://lulustream.com/u405p6qz5xpi",
				"filecode":"u405p6qz5xpi"
			}
		}
```

## Deleted Files

Get last deleted files list

**REQUEST**

```
GET https://lulustream.com/api/file/deleted?key=45pqv0iqypy3ephqk&last={last}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| last | Show files deleted in last X hours | 24 | INT |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-15 19:04:06",
			"status":200,
			"result":[\
				{\
					"file_code":"38j4wvxw164d",\
					"deleted_by":"me",\
					"deleted_ago_sec":"40",\
					"deleted":"2021-08-15 19:03:26",\
					"title":"Video 109779195"\
				}\
			]
		}
```

## File DMCA

Get files scheduled for DMCA delete

**REQUEST**

```
GET https://lulustream.com/api/file/dmca?key=45pqv0iqypy3ephqk&last={last}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| last | Show files reported last X hours only | 24 | INT |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-15 19:31:48",
			"status":200,
			"result":[\
				{\
					"file_code":"x2q5h0uhfzdu",\
					"del_in_sec":"42097",\
					"del_time":"2021-08-16 07:13:25"\
				}\
			]
		}
```

## Folder List

**REQUEST**

```
GET https://lulustream.com/api/folder/list?key=45pqv0iqypy3ephqk&fld_id={fld_id}&files=1
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| fld\_id | Parent folder id, default=0 | 25 | INT |  |
| files | Show file list in this folder | 1 | INT |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-15 19:54:22",
			"status":200,
			"result":{
				"folders":[\
					{\
						"name":"Breaking Bad",\
						"fld_id":"16",\
						"code":"4pwb4yvp7v"\
					},\
					{\
						"name":"Travis",\
						"fld_id":"15",\
						"code":"68dth39m76"\
					}\
				],
				"files":[\
					{\
						"thumbnail":"http://img.lulustream.com/abnormamorph_t.jpg",\
						"link":"http://lulustream.com/abnormamorph.html",\
						"file_code":"abnormamorph",\
						"canplay":1,\
						"length":"1560",\
						"views":"10",\
						"uploaded":"2021-08-20 20:37:22",\
						"public":"0",\
						"fld_id":"0",\
						"title":"Tri pljus dva 2012 SATRip"\
					}\
				]
			}
		}
```

## Folder Create

**REQUEST**

```
GET https://lulustream.com/api/folder/create?key=45pqv0iqypy3ephqk&name={name}&parent_id={parent_id}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| name | Folder name | New Videos | STRING |  |
| parent\_id | Parent Folder ID | 0 | INT |  |
| descr | Folder description | new stuff | STRING |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-18 20:32:46",
			"status":200,
			"result":
			{
				"fld_id":"29"
			}
		}
```

## Folder Edit

Update folder details, skipped fields won't be updated

**REQUEST**

```
GET https://lulustream.com/api/folder/edit?key=45pqv0iqypy3ephqk&fld_id={fld_id}&name={name}
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| fld\_id | Folder ID | 25 | INT |  |
| name | Folder name | Movies 2021 | STRING |  |
| parent\_id | Parent Folder ID | 0 | INT |  |
| descr | Folder description | old stuff | STRING |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-18 21:21:44",
			"status":200,
			"result":"true"
		}
```

## File Encodings

Get current encoding queues

**REQUEST**

```
GET https://lulustream.com/api/file/encodings?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| file\_code | Filter by File code | fb5asfuj2snh | STRING |  |

**RESPONSE**

```
{
			"msg":"OK",
			"server_time":"2021-08-18 21:44:09",
			"status":200,
			"result":[\
				{"link":"http://lulustream.com/fb5asfuj2snh.html","progress":15,"status":"ENCODING","title":"Test video","quality":"h","file_code":"fb5asfuj2snh"},\
				{"link":"http://lulustream.com/fb5asfuj2snh.html","progress":0,"status":"PENDING","title":"Test video","quality":"l","file_code":"fb5asfuj2snh"}\
			]
		}
```

## File URL Uploads

Get current encoding queues

**REQUEST**

```
GET https://lulustream.com/api/file/url_uploads?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| file\_code | Filter by File code | fb5asfuj2snh | STRING |  |

**RESPONSE**

```
{
			"requests_available":2,
			"msg":"OK",
			"server_time":"2022-06-14 20:30:20",
			"status":200,
			"result":[\
				{"remote_url":"https://lulustream.com/1.mp4","progress":0,"status":"PENDING","file_code":"","fld_id":"0"},\
				{"remote_url":"https://lulustream.com/2.mp4","progress":0,"status":"PENDING","file_code":"xyayxm9ajlys","fld_id":"7"}\
			]
		}
```

## File URL Uploads Actions

Get current encoding queues

**REQUEST**

```
GET https://lulustream.com/api/file/url_actions?key=45pqv0iqypy3ephqk
```

**PARAMETERS**

| Name | Description | Example | Format | Required |
| --- | --- | --- | --- | --- |
| key | API key | 45pqv0iqypy3ephqk | STRING |  |
| restart\_errors | Restart all failed uploads | 1 | INT |  |
| delete\_errors | Delete all failed uploads | 1 | INT |  |
| delete\_all | Delete all current uploads | 1 | INT |  |
| delete\_code | Delete specific uploads by code | xyayxm9ajlys,6bnyg8rnu11e | STRING |  |

**RESPONSE**

```
{
			"requests_available":2,
			"msg":"OK",
			"server_time":"2022-06-14 20:30:20",
			"status":200
		}